<template>
  <view class="container">
    <!-- 大转盘 -->
    <view class="dial">
      <!-- <image class="arrow" src="/assets/background/pin_arrow.png"></image> -->
      <image
        class="bg"
        :style="`transform: rotate(${degree}deg);transition: transform ${duration}s`"
        src="/static/images/pins.png"
      />
      <image class="go" @tap="handleTurn" src="/static/images/pins_go.webp"></image>
    </view>


  </view>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const degree = ref(0) // 转盘旋转角度
const duration = ref(0) // 动画持续时间
const isSpinning = ref(false) // 是否正在旋转

// 奖品配置（根据实际转盘图片的角度分布）
const prizes = ref([
  { name: '家属餐', probability: 70, startAngle: 0, endAngle: 252 },      // 0°~252° (252度范围)
  { name: '尿不湿', probability: 5, startAngle: 252, endAngle: 270 },      // 252°~270° (18度范围)
  { name: '产康次数', probability: 10, startAngle: 270, endAngle: 306 },   // 270°~306° (36度范围)
  { name: '续住一天', probability: 5, startAngle: 306, endAngle: 324 },    // 306°~324° (18度范围)
  { name: '婴儿游泳', probability: 10, startAngle: 324, endAngle: 360 }    // 324°~360° (36度范围)
])

// 当前中奖结果
const currentPrize = ref(null)

// 根据最终角度判断指针实际指向哪个奖品（用于调试）
const getPrizeByAngle = (finalAngle) => {
  const normalizedAngle = finalAngle % 360
  console.log(`最终角度: ${finalAngle}度, 标准化角度: ${normalizedAngle}度`)

  for (let i = 0; i < prizes.value.length; i++) {
    const prize = prizes.value[i]
    const startAngle = prize.startAngle
    const endAngle = prize.endAngle

    console.log(`检查奖品: ${prize.name}, 角度范围: ${startAngle}°~${endAngle}°`)

    if (endAngle > startAngle) {
      // 正常情况，不跨越0度
      if (normalizedAngle >= startAngle && normalizedAngle < endAngle) {
        console.log(`指针指向: ${prize.name}`)
        return prize
      }
    } else {
      // 跨越0度的情况（如324°~360°）
      if (normalizedAngle >= startAngle || normalizedAngle < endAngle) {
        console.log(`指针指向: ${prize.name}`)
        return prize
      }
    }
  }

  console.log('未找到匹配的奖品')
  return null
}

// 根据概率计算中奖结果
const calculatePrize = () => {
  const random = Math.random() * 100 // 0-100的随机数
  let cumulativeProbability = 0

  for (const prize of prizes.value) {
    cumulativeProbability += prize.probability
    if (random <= cumulativeProbability) {
      return prize
    }
  }

  // 默认返回第一个奖品（家属餐）
  return prizes.value[0]
}

// 计算转盘应该停止的角度
const calculateStopAngle = (targetPrize) => {
  // 指针指向上方正中间（0度），需要计算转盘应该转到的角度
  const startAngle = targetPrize.startAngle
  const endAngle = targetPrize.endAngle

  // 计算奖品区域的中心角度
  let prizeCenter
  if (endAngle > startAngle) {
    // 正常情况，不跨越0度
    prizeCenter = (startAngle + endAngle) / 2
  } else {
    // 跨越0度的情况（如324°~360°实际是324°~0°）
    prizeCenter = ((startAngle + endAngle + 360) / 2) % 360
  }

  // 转盘需要转动的角度，让奖品中心对准指针（0度位置）
  const stopAngle = 360 - prizeCenter

  console.log(`=== 角度计算详情 ===`)
  console.log(`目标奖品: ${targetPrize.name}`)
  console.log(`奖品角度范围: ${startAngle}°~${endAngle}°`)
  console.log(`奖品中心角度: ${prizeCenter}°`)
  console.log(`转盘需要转动: ${stopAngle}°`)
  console.log(`==================`)

  return stopAngle
}

// 点击转盘按钮
const handleTurn = () => {
  // 如果正在旋转，忽略点击
  if (isSpinning.value) {
    console.log('转盘正在旋转中，忽略点击')
    return
  }

  console.log('开始转盘旋转')

  // 计算中奖结果
  const winPrize = calculatePrize()
  currentPrize.value = winPrize
  console.log('中奖结果:', winPrize)

  // 计算停止角度（指向奖品正中间）
  const stopAngle = calculateStopAngle(winPrize)
  console.log('目标停止角度:', stopAngle)

  // 生成随机转动角度（超过1000度）
  const minRotation = 1000 // 最少转动1000度
  const maxRotation = 3600 // 最多转动3600度（10圈）
  const randomRotation = Math.random() * (maxRotation - minRotation) + minRotation

  console.log(`随机转动角度: ${randomRotation}度`)

  // 开始旋转
  isSpinning.value = true

  // 设置旋转参数
  const spinDuration = 4 // 旋转持续时间（秒）
  const totalRotation = randomRotation + stopAngle

  console.log(`=== 转盘转动详情 ===`)
  console.log(`当前转盘角度: ${degree.value}度`)
  console.log(`随机转动角度: ${randomRotation}度`)
  console.log(`精确停止角度: ${stopAngle}度`)
  console.log(`总转动角度: ${totalRotation}度`)
  console.log(`最终转盘角度: ${degree.value + totalRotation}度`)
  console.log(`==================`)

  // 设置动画时间和角度
  duration.value = spinDuration
  degree.value = degree.value + totalRotation

  // 旋转结束后显示结果
  setTimeout(() => {
    isSpinning.value = false
    console.log('转盘旋转结束')
    duration.value = 0

    // 调试：检查指针实际指向哪个奖品
    const actualPrize = getPrizeByAngle(degree.value)
    console.log(`=== 结果对比 ===`)
    console.log(`预期中奖: ${currentPrize.value.name}`)
    console.log(`实际指向: ${actualPrize ? actualPrize.name : '未知'}`)
    console.log(`===============`)

    // 显示中奖结果弹窗
    showPrizeResult()
  }, spinDuration * 1000)
}



// 显示中奖结果弹窗
const showPrizeResult = () => {
  if (currentPrize.value) {
    uni.showModal({
      title: '恭喜中奖！',
      content: `您获得了：${currentPrize.value.name}`,
      showCancel: false,
      confirmText: '确定',
      success: () => {
        console.log('用户确认中奖结果')
      }
    })
  }
}
</script>

<style lang="scss" scoped>
/* pages/shop/dial/index.wxss */

.container {
  width: 100vw;
  height: 100vh;
  padding-top:15vh ;
  background: #f8ae61;
}

.tip {
  margin: 210rpx 0 50rpx;
  color: #fff;
  font-size: 28rpx;
}

.tip text {
  color: #fa3c38;
}

/* dial */

.dial {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
}

.dial .bg {
  width: 700rpx;
  height: 700rpx;
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94); /* 先快后慢，慢慢停下来 */
}

.dial .arrow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%,-50%);
  width: 64rpx;
  z-index: 100;
}

.dial .go {
  position: absolute;
  margin: auto;
  width: 120rpx;
  height: 140rpx;
  z-index: 100;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.dial .go:hover {
  transform: scale(1.05);
}

.dial .go:active {
  transform: scale(0.95);
}

.dial .go .info {
  position: absolute;
  top: 0;
  display: grid;
  place-content: center;
  place-items: center;
  width: 160rpx;
  height: 160rpx;
  color: #fff;
}

.dial .go .info .title {
  font-size: 44rpx;
}

.dial .go .info .cost {
  font-size: 20rpx;
}



/* 活动中奖 */
.rule-wrap {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
}

.rule-wrap .rule-content {
  display: flex;
  width: 640rpx;
  margin-top: 20vh;
  padding: 40rpx;
  color: #333333;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 12rpx;
  flex-direction: column;
  align-items: center;
}

.rule-wrap .rule-content .image {
  width: 236rpx;
  height: 236rpx;
  margin: 60rpx 0 0;
}

.rule-wrap .rule-content .prize {
  width: 236rpx;
  margin: 60rpx 0 0;
}

.rule-wrap .rule-content .prize image {
  width: 236rpx;
  height: 236rpx;
}

.rule-wrap .rule-content .prize .level {
  top: 180rpx;
  width: 236rpx;
  height: 56rpx;
  line-height: 56rpx;
}

.rule-wrap .rule-content .text {
  margin: 40rpx 0 20rpx;
  color: #333;
  font-size: 28rpx;
  text-align: center;
}

.rule-wrap .rule-content button-common {
  width: calc(100% - 15rpx);
  margin: 15rpx 0 15rpx;
}

.rule-wrap .rule-content .common {
  height: 90rpx;
  border-radius: 50rpx;
  background: linear-gradient(180deg, #ffdbad 0%, #faa638 100%);
  color: #6a3c00;
  font-size: 28rpx;
}

.rule-wrap .rule-content .light {
  background: #fff;
  border: 2rpx solid #ccc;
  color: #666;
}

.rule-wrap .rule-close {
  position: absolute;
  right: 54rpx;
  top: calc(20vh - 120rpx);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, .6);
  color: #fff;
  text-align: center;
  line-height: 80rpx;
  font-size: 36rpx;
}




</style>